export interface Artifact {
  id: string
  type: 'image' | 'code' | 'markdown' | 'mermaid' | 'html' | 'json'
  title: string
  content: string
  metadata: {
    language?: string // for code
    mimeType?: string // for images
    size?: number
    createdAt: string
    messageId: string
    originalIndex?: number // position in message content
  }
  isActive: boolean
}

export interface ArtifactsState {
  // Current state
  isOpen: boolean
  isFullscreen: boolean
  currentArtifact: Artifact | null
  artifacts: Artifact[]
  sidebarWidth: number
  
  // Actions
  openArtifact: (artifact: Artifact) => void
  closeArtifacts: () => void
  toggleFullscreen: () => void
  addArtifact: (artifact: Artifact) => void
  removeArtifact: (id: string) => void
  setActiveArtifact: (id: string) => void
  setSidebarWidth: (width: number) => void
  clearArtifacts: () => void
}

export interface ArtifactDetectionResult {
  artifacts: Artifact[]
  processedContent: string // content with artifact placeholders
}

export interface ArtifactPattern {
  type: Artifact['type']
  regex: RegExp
  extract: (match: RegExpMatchArray, messageId: string, index: number) => Artifact
}

// Artifact detection patterns
export const ARTIFACT_PATTERNS: ArtifactPattern[] = [
  // Code blocks
  {
    type: 'code',
    regex: /```(\w+)?\n([\s\S]*?)```/g,
    extract: (match, messageId, index) => ({
      id: `artifact-${messageId}-${index}`,
      type: 'code',
      title: `Code${match[1] ? ` (${match[1]})` : ''}`,
      content: match[2],
      metadata: {
        language: match[1] || 'text',
        createdAt: new Date().toISOString(),
        messageId,
        originalIndex: index
      },
      isActive: false
    })
  },
  
  // Mermaid diagrams
  {
    type: 'mermaid',
    regex: /```mermaid\n([\s\S]*?)```/g,
    extract: (match, messageId, index) => ({
      id: `artifact-${messageId}-${index}`,
      type: 'mermaid',
      title: 'Mermaid Diagram',
      content: match[1],
      metadata: {
        createdAt: new Date().toISOString(),
        messageId,
        originalIndex: index
      },
      isActive: false
    })
  },
  
  // HTML content
  {
    type: 'html',
    regex: /<html[\s\S]*?<\/html>|<!DOCTYPE html[\s\S]*?<\/html>/gi,
    extract: (match, messageId, index) => ({
      id: `artifact-${messageId}-${index}`,
      type: 'html',
      title: 'HTML Document',
      content: match[0],
      metadata: {
        createdAt: new Date().toISOString(),
        messageId,
        originalIndex: index
      },
      isActive: false
    })
  },
  
  // JSON data
  {
    type: 'json',
    regex: /```json\n([\s\S]*?)```/g,
    extract: (match, messageId, index) => ({
      id: `artifact-${messageId}-${index}`,
      type: 'json',
      title: 'JSON Data',
      content: match[1],
      metadata: {
        language: 'json',
        createdAt: new Date().toISOString(),
        messageId,
        originalIndex: index
      },
      isActive: false
    })
  },
  
  // Markdown content (for large structured content)
  {
    type: 'markdown',
    regex: /^(#{1,6}\s+.*(?:\n(?!#{1,6}\s).*)*(?:\n#{1,6}\s+.*(?:\n(?!#{1,6}\s).*)*){2,})/gm,
    extract: (match, messageId, index) => ({
      id: `artifact-${messageId}-${index}`,
      type: 'markdown',
      title: 'Structured Document',
      content: match[1],
      metadata: {
        createdAt: new Date().toISOString(),
        messageId,
        originalIndex: index
      },
      isActive: false
    })
  }
]

// Helper function to detect artifacts in message content
export function detectArtifacts(content: string, messageId: string): ArtifactDetectionResult {
  const artifacts: Artifact[] = []
  let processedContent = content
  let artifactIndex = 0

  for (const pattern of ARTIFACT_PATTERNS) {
    let match
    const regex = new RegExp(pattern.regex.source, pattern.regex.flags)
    
    while ((match = regex.exec(content)) !== null) {
      const artifact = pattern.extract(match, messageId, artifactIndex++)
      artifacts.push(artifact)
      
      // Replace artifact content with placeholder button
      const placeholder = `[🎨 View ${artifact.title}]`
      processedContent = processedContent.replace(match[0], placeholder)
    }
  }

  return {
    artifacts,
    processedContent
  }
}

// Helper function to generate artifact title from content
export function generateArtifactTitle(type: Artifact['type'], content: string, language?: string): string {
  switch (type) {
    case 'code':
      return `Code${language ? ` (${language})` : ''}`
    case 'mermaid':
      return 'Mermaid Diagram'
    case 'html':
      return 'HTML Document'
    case 'json':
      return 'JSON Data'
    case 'markdown':
      // Try to extract first heading
      const headingMatch = content.match(/^#{1,6}\s+(.+)$/m)
      return headingMatch ? headingMatch[1] : 'Structured Document'
    case 'image':
      return 'Generated Image'
    default:
      return 'Artifact'
  }
}
