import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Message } from '../types'
import { <PERSON><PERSON>, <PERSON>r, <PERSON><PERSON>, <PERSON>, ChevronDown, ChevronRight, Brain, Refresh<PERSON><PERSON>, Pin } from './Icons'
import { useState } from 'react'
import FileAttachmentDisplay from './FileAttachmentDisplay'

interface MessageBubbleProps {
  message: Message;
  onRegenerate: (messageId: string) => void;
  onPinMessage: (messageId: string) => void;
}

interface ReasoningSection {
  type: 'thinking' | 'reasoning' | 'analysis'
  content: string
  title: string
}

interface ParsedContent {
  hasReasoning: boolean
  reasoningSections: ReasoningSection[]
  finalAnswer: string
}

// Utility function to parse reasoning content
const parseReasoningContent = (content: string): ParsedContent => {
  const reasoningPatterns = [
    // OpenAI o1 style thinking tags
    { pattern: /<thinking>([\s\S]*?)<\/thinking>/gi, type: 'thinking' as const, title: 'Thinking Process' },
    // DeepSeek R1 style thinking tags
    { pattern: /<think>([\s\S]*?)<\/think>/gi, type: 'thinking' as const, title: 'Reasoning' },
    // Generic reasoning patterns
    { pattern: /\*\*Reasoning:\*\*([\s\S]*?)(?=\*\*|$)/gi, type: 'reasoning' as const, title: 'Reasoning' },
    { pattern: /\*\*Analysis:\*\*([\s\S]*?)(?=\*\*|$)/gi, type: 'analysis' as const, title: 'Analysis' },
    // Step-by-step thinking
    { pattern: /Let me think through this step by step:([\s\S]*?)(?=\n\n|\n[A-Z]|$)/gi, type: 'thinking' as const, title: 'Step-by-step Analysis' },
    // Chain of thought patterns
    { pattern: /(?:First|Initially|Let me consider|I need to think about)([\s\S]*?)(?=\n\n(?:[A-Z]|In conclusion|Therefore|So)|$)/gi, type: 'thinking' as const, title: 'Chain of Thought' }
  ]

  const reasoningSections: ReasoningSection[] = []
  let processedContent = content

  // Extract reasoning sections
  for (const { pattern, type, title } of reasoningPatterns) {
    let match
    while ((match = pattern.exec(content)) !== null) {
      reasoningSections.push({
        type,
        content: match[1].trim(),
        title
      })
      // Remove the reasoning section from the final answer
      processedContent = processedContent.replace(match[0], '').trim()
    }
  }

  // Clean up the final answer
  const finalAnswer = processedContent
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .trim()

  return {
    hasReasoning: reasoningSections.length > 0,
    reasoningSections,
    finalAnswer: finalAnswer || content // Fallback to original content if nothing left
  }
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, onRegenerate, onPinMessage }) => {
  const [copied, setCopied] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set())
  const isUser = message.role === 'user'

  const handlePin = () => {
    onPinMessage(message.id)
  }
  
  // Parse content for reasoning sections
  const parsedContent = !isUser ? parseReasoningContent(message.content) : null

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy message:', error)
    }
  }

  const toggleSection = (index: number) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedSections(newExpanded)
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const MarkdownComponents = {
    // Custom component for links
    a: ({ node, ...props }) => (
      <a {...props} target="_blank" rel="noopener noreferrer" className="text-indigo-400 hover:underline" />
    ),
    // Custom component for code blocks
    code({ node, inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || '')
      return !inline && match ? (
        <div className="bg-neutral-900 rounded-md my-2">
          <div className="flex items-center justify-between px-4 py-1 bg-neutral-700/50 rounded-t-md">
            <span className="text-xs font-sans text-neutral-400">{match[1]}</span>
            <button 
              onClick={() => navigator.clipboard.writeText(String(children))}
              className="text-xs text-neutral-400 hover:text-white"
            >
              Copy
            </button>
          </div>
          <pre className="p-4 overflow-x-auto">
            <code {...props} className="text-sm">
              {children}
            </code>
          </pre>
        </div>
      ) : (
        <code {...props} className="bg-neutral-700 text-indigo-400 rounded-sm px-1 py-0.5 text-sm">
          {children}
        </code>
      )
    },
    // Add more custom components as needed for other elements
    h1: ({node, ...props}) => <h1 {...props} className="text-2xl font-bold my-4" />,
    h2: ({node, ...props}) => <h2 {...props} className="text-xl font-bold my-3" />,
    h3: ({node, ...props}) => <h3 {...props} className="text-lg font-bold my-2" />,
    ul: ({node, ...props}) => <ul {...props} className="list-disc list-inside my-2" />,
    ol: ({node, ...props}) => <ol {...props} className="list-decimal list-inside my-2" />,
    li: ({node, ...props}) => <li {...props} className="my-1" />,
    blockquote: ({node, ...props}) => <blockquote {...props} className="border-l-4 border-neutral-600 pl-4 my-2 italic" />,
  }

  return (
    <div className={`flex items-start gap-3 ${isUser ? 'justify-end' : ''}`}>
      {!isUser && (
        <div className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center shrink-0">
          <Bot className="h-4 w-4 text-white" />
        </div>
      )}
      
      <div className={`max-w-md ${isUser ? 'text-right' : ''}`}>
        <div className="group relative">
          <div className={`
            text-sm rounded-lg px-4 py-2 mb-1 break-words
            ${isUser
              ? 'bg-indigo-500 text-white'
              : 'bg-neutral-800 text-neutral-100'
            }
          `}>
            {/* Render reasoning sections if present */}
            {!isUser && parsedContent?.hasReasoning ? (
              <div className="space-y-3">
                {/* Reasoning sections */}
                {parsedContent.reasoningSections.map((section, index) => (
                  <div key={index} className="border border-neutral-700 rounded-lg overflow-hidden">
                    <button
                      onClick={() => toggleSection(index)}
                      className="w-full flex items-center gap-2 p-3 bg-neutral-700/50 hover:bg-neutral-700 transition-colors text-left"
                    >
                      {expandedSections.has(index) ? (
                        <ChevronDown className="h-4 w-4 text-neutral-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-neutral-400" />
                      )}
                      <Brain className="h-4 w-4 text-purple-400" />
                      <span className="font-medium text-neutral-300">{section.title}</span>
                      <span className="text-xs text-neutral-500 ml-auto">
                        {section.content.length} chars
                      </span>
                    </button>

                    {expandedSections.has(index) && (
                      <div className="p-3 bg-neutral-800/50 border-t border-neutral-700">
                        <div className="prose prose-invert prose-sm max-w-none">
                          <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                            {section.content}
                          </ReactMarkdown>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Final answer */}
                {parsedContent.finalAnswer && (
                  <div className="pt-3 border-t border-neutral-700">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="h-2 w-2 bg-green-400 rounded-full"></div>
                      <span className="text-xs font-medium text-green-400 uppercase tracking-wide">Final Answer</span>
                    </div>
                    <div className="prose prose-invert prose-sm max-w-none">
                      <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                        {parsedContent.finalAnswer}
                      </ReactMarkdown>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              /* Regular message content */
              <div className="prose prose-invert prose-sm max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                  {message.content}
                </ReactMarkdown>
              </div>
            )}

            {/* File attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <FileAttachmentDisplay
                files={message.attachments.map(att => att.file!).filter(Boolean)}
                mode="full"
                showPreview={true}
              />
            )}
          </div>

          {/* Action buttons */}
          <div className={`
            absolute top-2 ${isUser ? 'left-2' : 'right-2'}
            opacity-0 group-hover:opacity-100 transition-opacity
            flex gap-1
          `}>
            <button
              onClick={handleCopy}
              className="p-1 rounded hover:bg-black/20 text-xs"
              title="Copy message"
            >
              {copied ? (
                <Check className="h-3 w-3" />
              ) : (
                <Copy className="h-3 w-3" />
              )}
            </button>
            {!isUser && (
              <button
                onClick={handlePin}
                className={`p-1 rounded hover:bg-black/20 text-xs ${message.is_pinned ? 'text-yellow-400' : ''}`}
                title={message.is_pinned ? "Unpin message" : "Pin message"}
              >
                <Pin className="h-3 w-3" />
              </button>
            )}
          </div>
        </div>
        
        <div className={`flex items-center gap-2 text-xs text-neutral-500 ${isUser ? 'justify-end' : ''}`}>
          <span>{formatTime(message.created_at)}</span>
          {message.model && !isUser && (
            <>
              <span>•</span>
              <span className="text-indigo-400">{message.model}</span>
            </>
          )}
          {message.is_pinned === 1 && (
            <>
              <span>•</span>
              <span className="text-yellow-400 flex items-center gap-1">
                <Pin className="h-3 w-3" />
                Pinned
              </span>
            </>
          )}
          {!isUser && (
            <div className="flex items-center gap-2">
              <button onClick={() => onRegenerate(message.id)} className="hover:text-white">
                <RefreshCw className="h-3 w-3" />
              </button>
              <button onClick={handleCopy} className="hover:text-white">
                {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
              </button>
            </div>
          )}
        </div>
      </div>
      
      {isUser && (
        <div className="h-8 w-8 rounded-full bg-neutral-700 flex items-center justify-center shrink-0">
          <User className="h-4 w-4 text-neutral-300" />
        </div>
      )}
    </div>
  )
}

export default MessageBubble
