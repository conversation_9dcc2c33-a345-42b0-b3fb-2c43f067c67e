import React, { useState, useRef, useEffect } from 'react'
import { useAppStore } from '../store'
import { Send, Plus, Settings, Loader2, CheckCircle, AlertCircle } from './Icons'
import ChatSettingsDrawer from './ChatSettingsDrawer'
import AttachmentMenu from './AttachmentMenu'
import FilePicker from './FilePicker'
import FileAutocomplete from './FileAutocomplete'
import FileAttachments from './FileAttachments'
import { FileRecord } from '../types'

// Toast notification component
const Toast: React.FC<{ message: string; type: 'success' | 'error' | 'warning'; onClose: () => void }> = ({ message, type, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 4000)
    return () => clearTimeout(timer)
  }, [onClose])

  const getIcon = () => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5" />
      case 'error': return <AlertCircle className="h-5 w-5" />
      case 'warning': return <AlertCircle className="h-5 w-5" />
    }
  }

  const getStyles = () => {
    switch (type) {
      case 'success': return 'bg-green-900/80 border-green-700 text-green-100'
      case 'error': return 'bg-red-900/80 border-red-700 text-red-100'
      case 'warning': return 'bg-yellow-900/80 border-yellow-700 text-yellow-100'
    }
  }

  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg backdrop-blur-lg border ${getStyles()}`}>
      <div className="flex items-center gap-2">
        {getIcon()}
        <span className="text-sm font-medium">{message}</span>
        <button
          onClick={onClose}
          className="ml-2 text-current hover:opacity-70"
        >
          ×
        </button>
      </div>
    </div>
  )
}

const InputArea: React.FC = () => {
  const { currentConversationId, sendMessage, isLoading, models, settings } = useAppStore()
  const [input, setInput] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false)
  const [showFilePicker, setShowFilePicker] = useState(false)
  const [filePickerMode, setFilePickerMode] = useState<'files' | 'images'>('files')
  const [attachedFiles, setAttachedFiles] = useState<FileRecord[]>([])
  const [showAutocomplete, setShowAutocomplete] = useState(false)
  const [autocompleteQuery, setAutocompleteQuery] = useState('')
  const [autocompletePosition, setAutocompletePosition] = useState({ top: 0, left: 0 })
  const [cursorPosition, setCursorPosition] = useState(0)
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' | 'warning' } | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const attachButtonRef = useRef<HTMLButtonElement>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || !currentConversationId || isLoading) return

    const message = input.trim()
    setInput('')

    // Clear attached files after sending
    const filesToSend = [...attachedFiles]
    setAttachedFiles([])

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    try {
      // TODO: Update sendMessage to handle file attachments
      await sendMessage(message, currentConversationId, filesToSend)
    } catch (error) {
      console.error('Error sending message:', error)
      // The error will be handled by the store and displayed in the chat
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Don't handle Enter if autocomplete is open (let autocomplete handle it)
    if (showAutocomplete && (e.key === 'Enter' || e.key === 'ArrowUp' || e.key === 'ArrowDown' || e.key === 'Escape')) {
      return
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [input])

  // Optimized clipboard paste handler for images
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      // Only handle paste if input area is focused
      if (document.activeElement !== textareaRef.current) return

      const items = e.clipboardData?.items
      if (!items) return

      for (let i = 0; i < items.length; i++) {
        const item = items[i]

        if (item.type.startsWith('image/')) {
          e.preventDefault()

          try {
            const file = item.getAsFile()
            if (!file) continue

            // Create a unique filename
            const timestamp = Date.now()
            const extension = file.type.split('/')[1] || 'png'
            const filename = `pasted-image-${timestamp}.${extension}`

            // Convert file to base64 and save to uploads
            const arrayBuffer = await file.arrayBuffer()
            const uint8Array = new Uint8Array(arrayBuffer)
            const base64String = btoa(String.fromCharCode(...uint8Array))

            if (window.electronAPI?.files) {
              // Save the image to the uploads folder
              const tempPath = await window.electronAPI.files.saveContentAsFile(
                base64String,
                filename,
                'Uploads'
              )

              // Index the file (metadata only)
              const fileId = await window.electronAPI.files.indexFile(tempPath)

              if (fileId) {
                // Get the file record and add to attachments
                const files = await window.electronAPI.files.getIndexedFiles()
                const newFile = files.find(f => f.id === fileId)
                if (newFile) {
                  setAttachedFiles(prev => [...prev, newFile])
                }
              }
            }
          } catch (error) {
            console.error('Error processing pasted image:', error)
          }

          break // Only process the first image
        }
      }
    }

    document.addEventListener('paste', handlePaste)
    return () => document.removeEventListener('paste', handlePaste)
  }, [])

  const handleFileSelect = () => {
    setFilePickerMode('files')
    setShowFilePicker(true)
  }

  const handleImageSelect = () => {
    setFilePickerMode('images')
    setShowFilePicker(true)
  }

  const handleFilesSelected = (files: FileRecord[]) => {
    setAttachedFiles(prev => [...prev, ...files])

    // Check if any files need vectorization
    const unprocessedFiles = files.filter(file => !file.extracted_content)
    if (unprocessedFiles.length > 0) {
      setToast({
        message: `${unprocessedFiles.length} file(s) need to be vectorized for AI processing. Click the lightning icon to vectorize.`,
        type: 'warning'
      })
    }
  }

  const handleVectorizeFile = async (fileId: string) => {
    try {
      if (window.electronAPI?.files) {
        const success = await window.electronAPI.files.processFileContent(fileId)
        if (success) {
          // Refresh the file data
          const files = await window.electronAPI.files.getIndexedFiles()
          const updatedFile = files.find(f => f.id === fileId)
          if (updatedFile) {
            setAttachedFiles(prev => prev.map(f => f.id === fileId ? updatedFile : f))
            setToast({
              message: `File "${updatedFile.filename}" has been vectorized successfully!`,
              type: 'success'
            })
          }
        } else {
          setToast({
            message: 'Failed to vectorize file. Please try again.',
            type: 'error'
          })
        }
      }
    } catch (error) {
      console.error('Error vectorizing file:', error)
      setToast({
        message: 'Error occurred while vectorizing file.',
        type: 'error'
      })
    }
  }



  // Debounce autocomplete to avoid excessive API calls
  const debounceTimeoutRef = useRef<NodeJS.Timeout>()

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    const cursorPos = e.target.selectionStart
    setInput(value)
    setCursorPosition(cursorPos)

    // Clear previous debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    // Check for @ symbol for file autocomplete
    const textBeforeCursor = value.substring(0, cursorPos)
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/)

    if (atMatch) {
      const query = atMatch[1]

      // Debounce the autocomplete to avoid excessive searches
      debounceTimeoutRef.current = setTimeout(() => {
        setAutocompleteQuery(query)

        // Simple positioning - show above textarea
        if (textareaRef.current) {
          const rect = textareaRef.current.getBoundingClientRect()
          setAutocompletePosition({
            top: rect.top - 250, // Show above the textarea
            left: rect.left
          })
          setShowAutocomplete(true)
        }
      }, 150) // 150ms debounce
    } else {
      setShowAutocomplete(false)
    }
  }

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  const handleFileAutocompleteSelect = (file: FileRecord) => {
    const textBeforeCursor = input.substring(0, cursorPosition)
    const textAfterCursor = input.substring(cursorPosition)
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/)

    if (atMatch) {
      const beforeAt = textBeforeCursor.substring(0, atMatch.index)
      const newText = beforeAt + `@${file.filename} ` + textAfterCursor
      setInput(newText)

      // Set cursor position after the inserted filename
      const newCursorPos = beforeAt.length + file.filename.length + 2
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)
          textareaRef.current.focus()
        }
      }, 0)
    }

    setShowAutocomplete(false)
  }

  if (!currentConversationId) {
    return null
  }

  return (
    <div className="border-t border-neutral-800 bg-neutral-900/60 backdrop-blur-lg">
      <form onSubmit={handleSubmit} className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {/* File Attachments */}
          <FileAttachments
            attachedFiles={attachedFiles}
            onRemoveFile={(fileId) => {
              setAttachedFiles(prev => prev.filter(f => f.id !== fileId))
            }}
            onVectorizeFile={handleVectorizeFile}
          />

          <div className="flex items-end gap-3">
          {/* Attachment button */}
          <button
            ref={attachButtonRef}
            type="button"
            onClick={() => setShowAttachmentMenu(true)}
            className="h-10 w-10 flex items-center justify-center rounded-lg hover:bg-neutral-800 transition-colors shrink-0"
            title="Attach file"
          >
            <Plus className="h-5 w-5" />
          </button>

          {/* Input field */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={input}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Type your message… (Shift+Enter for new line, @filename to reference files)"
              className="w-full bg-neutral-900 border border-neutral-800 rounded-lg px-4 py-3 pr-12 text-sm placeholder-neutral-500 focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none resize-none min-h-[44px] max-h-32"
              rows={1}
              disabled={isLoading}
            />
            
            {/* Character count */}
            {input.length > 0 && (
              <div className="absolute bottom-1 right-12 text-xs text-neutral-500">
                {input.length}
              </div>
            )}
          </div>

          {/* Settings button */}
          <button
            type="button"
            onClick={() => setShowSettings(true)}
            className="h-10 w-10 flex items-center justify-center rounded-lg transition-colors shrink-0 bg-neutral-800 hover:bg-neutral-700 text-neutral-400 hover:text-neutral-300"
            title="Chat settings"
          >
            <Settings className="h-4 w-4" />
          </button>

          {/* Send button */}
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className={`
              h-10 w-10 flex items-center justify-center rounded-lg transition-colors shrink-0
              ${input.trim() && !isLoading
                ? 'bg-indigo-500 hover:bg-indigo-600 text-white'
                : 'bg-neutral-800 text-neutral-500 cursor-not-allowed'
              }
            `}
            title="Send message"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </button>
          </div>
        </div>

        {/* Model selector and options */}
        <div className="max-w-4xl mx-auto mt-2 flex items-center justify-between text-xs text-neutral-500">
          <div className="flex items-center gap-4">
            <span>Model: {settings.selectedModel ?
              models.find(m => m.id === settings.selectedModel)?.name || settings.selectedModel
              : 'No model selected'}</span>
            <span>•</span>
            <span>Temp: {settings.temperature?.toFixed(1) || '0.7'}</span>
            <span>•</span>
            <span>Max: {settings.maxTokens?.toLocaleString() || '4K'}</span>
            {settings.topP && (
              <>
                <span>•</span>
                <span>Top-P: {settings.topP.toFixed(2)}</span>
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span>Shift+Enter for new line</span>
          </div>
        </div>
      </form>

      {/* Attachment Menu */}
      <AttachmentMenu
        isOpen={showAttachmentMenu}
        onClose={() => setShowAttachmentMenu(false)}
        onFileSelect={handleFileSelect}
        onImageSelect={handleImageSelect}
        anchorRef={attachButtonRef}
      />

      {/* File Picker */}
      <FilePicker
        isOpen={showFilePicker}
        onClose={() => setShowFilePicker(false)}
        onFileSelect={handleFilesSelected}
        mode={filePickerMode}
      />

      {/* File Autocomplete */}
      <FileAutocomplete
        isOpen={showAutocomplete}
        query={autocompleteQuery}
        position={autocompletePosition}
        onSelect={handleFileAutocompleteSelect}
        onClose={() => setShowAutocomplete(false)}
      />

      {/* Chat Settings Drawer */}
      <ChatSettingsDrawer
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Toast Notifications */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  )
}

export default InputArea
