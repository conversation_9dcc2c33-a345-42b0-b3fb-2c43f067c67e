import React, { useState, useEffect } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Bot, ChevronDown, ChevronRight, Brain, Loader2 } from './Icons'
import { useArtifactDetection } from '../hooks/useArtifactDetection'
import { FloatingArtifactIndicator } from './artifacts/controls/InlineArtifactButton'

interface ReasoningSection {
  type: 'thinking' | 'reasoning' | 'analysis'
  content: string
  title: string
  isComplete: boolean
}

interface ParsedStreamingContent {
  hasReasoning: boolean
  reasoningSections: ReasoningSection[]
  finalAnswer: string
  isStreaming: boolean
}

interface StreamingMessageBubbleProps {
  content: string
}

// Enhanced parsing for streaming content
const parseStreamingReasoningContent = (content: string): ParsedStreamingContent => {
  const reasoningPatterns = [
    // OpenAI o1 style thinking tags
    { pattern: /<thinking>([\s\S]*?)(?:<\/thinking>|$)/gi, type: 'thinking' as const, title: 'Thinking Process' },
    // DeepSeek R1 style thinking tags
    { pattern: /<think>([\s\S]*?)(?:<\/think>|$)/gi, type: 'thinking' as const, title: 'Reasoning' },
    // Generic reasoning patterns
    { pattern: /\*\*Reasoning:\*\*([\s\S]*?)(?=\*\*|$)/gi, type: 'reasoning' as const, title: 'Reasoning' },
    { pattern: /\*\*Analysis:\*\*([\s\S]*?)(?=\*\*|$)/gi, type: 'analysis' as const, title: 'Analysis' },
    // Step-by-step thinking
    { pattern: /Let me think through this step by step:([\s\S]*?)(?=\n\n|\n[A-Z]|$)/gi, type: 'thinking' as const, title: 'Step-by-step Analysis' },
    // Chain of thought patterns
    { pattern: /(?:First|Initially|Let me consider|I need to think about)([\s\S]*?)(?=\n\n(?:[A-Z]|In conclusion|Therefore|So)|$)/gi, type: 'thinking' as const, title: 'Chain of Thought' }
  ]

  const reasoningSections: ReasoningSection[] = []
  let processedContent = content

  // Extract reasoning sections
  for (const { pattern, type, title } of reasoningPatterns) {
    let match
    const regex = new RegExp(pattern.source, pattern.flags)
    while ((match = regex.exec(content)) !== null) {
      const sectionContent = match[1].trim()
      const isComplete = match[0].includes('</thinking>') || match[0].includes('</think>') || !match[0].endsWith(sectionContent)
      
      reasoningSections.push({
        type,
        content: sectionContent,
        title,
        isComplete
      })
      
      // Remove the reasoning section from the final answer
      processedContent = processedContent.replace(match[0], '').trim()
    }
  }

  // Clean up the final answer
  const finalAnswer = processedContent
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .trim()

  return {
    hasReasoning: reasoningSections.length > 0,
    reasoningSections,
    finalAnswer: finalAnswer || content, // Fallback to original content if nothing left
    isStreaming: true
  }
}

const StreamingMessageBubble: React.FC<StreamingMessageBubbleProps> = ({ content }) => {
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set())
  const [streamingArtifacts, setStreamingArtifacts] = useState<any[]>([])
  const { processStreamingMessage } = useArtifactDetection()

  // Detect artifacts in streaming content
  useEffect(() => {
    if (content) {
      const result = processStreamingMessage(content, 'streaming-message')
      setStreamingArtifacts(result.newArtifacts)
    }
  }, [content, processStreamingMessage])
  const [parsedContent, setParsedContent] = useState<ParsedStreamingContent>(() => 
    parseStreamingReasoningContent(content)
  )

  // Update parsed content when content changes
  useEffect(() => {
    setParsedContent(parseStreamingReasoningContent(content))
  }, [content])

  const toggleSection = (index: number) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedSections(newExpanded)
  }

  const MarkdownComponents = {
    // Custom component for links
    a: ({ node, ...props }) => (
      <a {...props} target="_blank" rel="noopener noreferrer" className="text-indigo-400 hover:underline" />
    ),
    // Custom component for code blocks
    code({ node, inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || '')
      return !inline && match ? (
        <div className="bg-neutral-900 rounded-md my-2">
          <div className="flex items-center justify-between px-4 py-1 bg-neutral-700/50 rounded-t-md">
            <span className="text-xs font-sans text-neutral-400">{match[1]}</span>
            <button 
              onClick={() => navigator.clipboard.writeText(String(children))}
              className="text-xs text-neutral-400 hover:text-white"
            >
              Copy
            </button>
          </div>
          <pre className="p-4 overflow-x-auto">
            <code {...props} className="text-sm">
              {children}
            </code>
          </pre>
        </div>
      ) : (
        <code {...props} className="bg-neutral-700 text-indigo-400 rounded-sm px-1 py-0.5 text-sm">
          {children}
        </code>
      )
    },
    // Add more custom components as needed for other elements
    h1: ({node, ...props}) => <h1 {...props} className="text-2xl font-bold my-4" />,
    h2: ({node, ...props}) => <h2 {...props} className="text-xl font-bold my-3" />,
    h3: ({node, ...props}) => <h3 {...props} className="text-lg font-bold my-2" />,
    ul: ({node, ...props}) => <ul {...props} className="list-disc list-inside my-2" />,
    ol: ({node, ...props}) => <ol {...props} className="list-decimal list-inside my-2" />,
    li: ({node, ...props}) => <li {...props} className="my-1" />,
    blockquote: ({node, ...props}) => <blockquote {...props} className="border-l-4 border-neutral-600 pl-4 my-2 italic" />,
  }

  return (
    <div className="flex items-start gap-3">
      <div className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center">
        <Bot className="h-4 w-4 text-white" />
      </div>
      <div className="flex-1 max-w-4xl">
        <div className="bg-neutral-800 rounded-lg px-4 py-3">
          {/* Render reasoning sections if present */}
          {parsedContent.hasReasoning ? (
            <div className="space-y-3">
              {/* Reasoning sections */}
              {parsedContent.reasoningSections.map((section, index) => (
                <div key={index} className="border border-neutral-700 rounded-lg overflow-hidden">
                  <button
                    onClick={() => toggleSection(index)}
                    className="w-full flex items-center gap-2 p-3 bg-neutral-700/50 hover:bg-neutral-700 transition-colors text-left"
                  >
                    {expandedSections.has(index) ? (
                      <ChevronDown className="h-4 w-4 text-neutral-400" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-neutral-400" />
                    )}
                    <Brain className="h-4 w-4 text-purple-400" />
                    <span className="font-medium text-neutral-300">{section.title}</span>
                    <div className="flex items-center gap-2 ml-auto">
                      <span className="text-xs text-neutral-500">
                        {section.content.length} chars
                      </span>
                      {!section.isComplete && (
                        <Loader2 className="h-3 w-3 text-purple-400 animate-spin" />
                      )}
                    </div>
                  </button>

                  {expandedSections.has(index) && (
                    <div className="p-3 bg-neutral-800/50 border-t border-neutral-700">
                      <div className="prose prose-invert prose-sm max-w-none">
                        <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                          {section.content}
                        </ReactMarkdown>
                        {!section.isComplete && (
                          <span className="inline-block w-2 h-4 bg-purple-400 ml-1 animate-pulse"></span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Final answer */}
              {parsedContent.finalAnswer && (
                <div className="pt-3 border-t border-neutral-700">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-2 w-2 bg-green-400 rounded-full"></div>
                    <span className="text-xs font-medium text-green-400 uppercase tracking-wide">Response</span>
                  </div>
                  <div className="prose prose-invert prose-sm max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                      {parsedContent.finalAnswer}
                    </ReactMarkdown>
                    <span className="inline-block w-2 h-4 bg-indigo-400 ml-1 animate-pulse"></span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Regular streaming message content */
            <div className="prose prose-invert prose-sm max-w-none">
              <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                {content}
              </ReactMarkdown>
              <span className="inline-block w-2 h-4 bg-indigo-400 ml-1 animate-pulse"></span>
            </div>
          )}
        </div>

        {/* Floating artifact indicator for streaming */}
        {streamingArtifacts.length > 0 && (
          <FloatingArtifactIndicator artifacts={streamingArtifacts} />
        )}
      </div>
    </div>
  )
}

export default StreamingMessageBubble
